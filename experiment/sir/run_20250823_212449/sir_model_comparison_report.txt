================================================================================
SIR模型比较分析报告
================================================================================
生成时间: 2025-08-24 09:44:23

模型说明:
1. 常系数模型: dS/dt = -β·S·I/N, dI/dt = β·S·I/N - γ·I, dR/dt = γ·I
2. 线性变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀ + λ₁·t, μ(t) = μ₀ + μ₁·t
3. 指数变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀·exp(λ₁·t), μ(t) = μ₀·exp(μ₁·t)
4. 多项式变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀ + λ₁·t + λ₂·t², μ(t) = μ₀ + μ₁·t + μ₂·t²

最佳模型总结:
----------------------------------------
总群体: exponential模型 (MSE: 0.00039649)

------------------------------------------------------------
总群体 详细比较结果
------------------------------------------------------------

1. exponential模型:
   MSE: 0.00039649
   模型类型: 指数变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.328571·exp(-0.088009·t)
     恢复率: μ(t) = 0.031248·exp(-0.008317·t)
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -0.328571·exp(-0.088009·t)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = 0.328571·exp(-0.088009·t)·I·S - 0.031248·exp(-0.008317·t)·I
     dR/dt = μ(t)·I = 0.031248·exp(-0.008317·t)·I
   平均R₀: 3.273185
   原始参数: [0.32857103207315547, -0.08800859601286361, 0.031248402362066066, -0.008317309231221821]
   R² (S,I,R): (0.9932, 0.9664, 0.9901)
   ★ 最佳模型

2. linear模型:
   MSE: 0.00047339
   模型类型: 线性变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.254609 + -0.009490·t
     恢复率: μ(t) = 0.037737 + -0.000575·t
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -(0.254609 + -0.009490·t)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = (0.254609 + -0.009490·t)·I·S - (0.037737 + -0.000575·t)·I
     dR/dt = μ(t)·I = (0.037737 + -0.000575·t)·I
   平均R₀: 3.181237
   原始参数: [0.25460909805427046, -0.009489511493096783, 0.03773681876506876, -0.000574624033472295]
   R² (S,I,R): (0.9897, 0.9703, 0.9862)

3. polynomial模型:
   MSE: 0.00109136
   模型类型: 多项式变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.149841 + 0.015511·t + -0.001053·t²
     恢复率: μ(t) = 0.047964 + -0.001413·t + 0.000014·t²
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -(0.149841 + 0.015511·t + -0.001053·t²)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = (0.149841 + 0.015511·t + -0.001053·t²)·I·S - (0.047964 + -0.001413·t + 0.000014·t²)·I
     dR/dt = μ(t)·I = (0.047964 + -0.001413·t + 0.000014·t²)·I
   平均R₀: 3.047324
   原始参数: [0.14984057676099372, 0.015510834721225627, -0.0010531888805363823, 0.047963910870926246, -0.0014126122340174573, 1.400560589500094e-05]
   R² (S,I,R): (0.9750, 0.9222, 0.9795)

4. constant模型:
   MSE: 0.01714542
   模型类型: 常系数模型
   拟合后的率函数:
     传播率: β = 0.130663
     恢复率: γ = 0.042989
   拟合后的微分方程:
     dS/dt = -0.130663·S·I/N
     dI/dt = 0.130663·S·I/N - 0.042989·I
     dR/dt = 0.042989·I
   R₀: 3.039433
   R² (S,I,R): (0.5222, -0.1249, 0.8492)

============================================================
模型选择建议
============================================================
最佳模型: exponential模型

建议:
1. exponential变系数模型表现最佳，说明传播动力学存在时变特征
2. 变系数模型能更好地捕捉传播过程中的动态变化
3. 建议在实际应用中使用变系数模型进行预测和分析
