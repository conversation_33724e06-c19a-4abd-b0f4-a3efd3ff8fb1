虚拟社区SIR模型对比分析报告

生成时间：2025年08月24日 10:19:20
对比数据集：Run_20250820 vs Run_20250823
分析轮次：第1轮至第40轮，共40轮

==================================================
一、总体传播情况对比
==================================================

1. 感染峰值对比：
   - Run_20250820：25.0%（第30轮）
   - Run_20250823：34.0%（第20轮）
   - 差异：9.0%

2. 最终恢复率对比：
   - Run_20250820：30.0%
   - Run_20250823：26.0%
   - 差异：4.0%

3. 易感人群减少对比：
   - Run_20250820：45.0%
   - Run_20250823：45.0%
   - 差异：0.0%

==================================================
二、知识分子群体对比
==================================================

1. 感染峰值对比：
   - Run_20250820：16.0%（第8轮）
   - Run_20250823：0.0%（第1轮）
   - 差异：16.0%

2. 最终恢复率对比：
   - Run_20250820：30.0%
   - Run_20250823：0.0%
   - 差异：30.0%

==================================================
三、普通群众群体对比
==================================================

1. 感染峰值对比：
   - Run_20250820：36.0%（第27轮）
   - Run_20250823：34.0%（第20轮）
   - 差异：2.0%

2. 最终恢复率对比：
   - Run_20250820：30.0%
   - Run_20250823：26.0%
   - 差异：4.0%

==================================================
四、对比总结
==================================================

基于40轮的对比分析，两个数据集在SIR传播模式上的主要差异：

1. 传播强度：数据集2传播更强

2. 恢复能力：数据集1恢复能力更强

3. 群体差异：知识分子群体和普通群众群体在两个数据集中表现出显著的差异模式

==================================================
报告结束
==================================================
