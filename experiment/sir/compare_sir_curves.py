#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import glob
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from datetime import datetime
import logging
import argparse
from matplotlib.backends.backend_pdf import PdfPages

# 设置字体
plt.rcParams['font.family'] = 'Times New Roman'  # 使用Times New Roman字体
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_sir_data(log_dir: str):
    """
    从日志目录加载所有SIR数据

    Args:
        log_dir: 日志目录路径

    Returns:
        包含所有SIR数据的字典
    """
    if not os.path.exists(log_dir):
        logger.error(f"指定的日志目录不存在: {log_dir}")
        return {}

    sir_files = glob.glob(os.path.join(log_dir, "*sir_metrics.json"))
    sir_data = {}

    for file_path in sir_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取轮次和阶段信息
            round_num = data.get('round', 0)
            stage = data.get('stage', '')
            key = f"round_{round_num}_{stage}"

            sir_data[key] = data

        except Exception as e:
            logger.warning(f"读取文件 {file_path} 时出错: {e}")
            continue

    logger.info(f"从 {log_dir} 成功加载 {len(sir_data)} 个SIR数据文件")
    return sir_data

def extract_sir_trends(sir_data: dict):
    """
    从SIR数据中提取趋势数据
    
    Args:
        sir_data: SIR数据字典
        
    Returns:
        包含总体和各群体趋势数据的字典
    """
    # 获取所有轮次
    rounds = set()
    for key in sir_data.keys():
        if 'round_' in key:
            round_num = int(key.split('_')[1])
            rounds.add(round_num)
    
    rounds = sorted(list(rounds))
    logger.info(f"发现 {len(rounds)} 个轮次的数据: {rounds}")

    if not rounds:
        logger.error("未找到任何轮次数据")
        return None

    # 初始化趋势数据
    trends = {
        'rounds': rounds,
        'total': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        },
        'intellectual': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        },
        'regular': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        }
    }
    
    # 提取每轮的数据（优先使用after数据，如果没有则使用before数据）
    for round_num in rounds:
        after_key = f"round_{round_num}_after"
        before_key = f"round_{round_num}_before"
        
        # 选择数据源
        if after_key in sir_data:
            data = sir_data[after_key]
        elif before_key in sir_data:
            data = sir_data[before_key]
        else:
            logger.warning(f"轮次 {round_num} 没有找到SIR数据")
            # 填充空数据
            trends['total']['susceptible'].append(0)
            trends['total']['infected'].append(0)
            trends['total']['recovered'].append(0)
            trends['intellectual']['susceptible'].append(0)
            trends['intellectual']['infected'].append(0)
            trends['intellectual']['recovered'].append(0)
            trends['regular']['susceptible'].append(0)
            trends['regular']['infected'].append(0)
            trends['regular']['recovered'].append(0)
            continue
        
        # 提取总体数据
        trends['total']['susceptible'].append(data.get('susceptible', {}).get('ratio', 0))
        trends['total']['infected'].append(data.get('infected', {}).get('ratio', 0))
        trends['total']['recovered'].append(data.get('recovered', {}).get('ratio', 0))
        
        # 提取群体数据
        group_analysis = data.get('group_analysis', {})
        
        # 知识分子群体
        intellectual_data = group_analysis.get('intellectual', {})
        trends['intellectual']['susceptible'].append(intellectual_data.get('susceptible', {}).get('ratio', 0))
        trends['intellectual']['infected'].append(intellectual_data.get('infected', {}).get('ratio', 0))
        trends['intellectual']['recovered'].append(intellectual_data.get('recovered', {}).get('ratio', 0))
        
        # 普通群众群体
        regular_data = group_analysis.get('regular', {})
        trends['regular']['susceptible'].append(regular_data.get('susceptible', {}).get('ratio', 0))
        trends['regular']['infected'].append(regular_data.get('infected', {}).get('ratio', 0))
        trends['regular']['recovered'].append(regular_data.get('recovered', {}).get('ratio', 0))
    
    return trends

def plot_comparison_sir_curves(trends1: dict, trends2: dict, label1: str, label2: str, 
                              output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/sir"):
    """
    绘制两个目录的SIR对比曲线图

    Args:
        trends1: 第一个目录的趋势数据
        trends2: 第二个目录的趋势数据
        label1: 第一个目录的标签
        label2: 第二个目录的标签
        output_dir: 输出目录
    """
    # 创建比较结果目录
    comparison_dir = os.path.join(output_dir, f"comparison_{label1}_vs_{label2}")
    os.makedirs(comparison_dir, exist_ok=True)
    
    # 设置图形样式
    plt.style.use('default')
    
    # 莫兰迪色系：深色和浅色版本
    colors_dark = {
        'susceptible': '#6B8E6B',  # 深莫兰迪绿色
        'infected': '#B04A4A',     # 深莫兰迪红色
        'recovered': '#4A6BB0'     # 深莫兰迪蓝色
    }
    
    colors_light = {
        'susceptible': '#A8C8A8',  # 浅莫兰迪绿色
        'infected': '#E08080',     # 浅莫兰迪红色
        'recovered': '#8FA8E0'     # 浅莫兰迪蓝色
    }
    
    # 使用较短的轮次范围（取两者的交集）
    rounds1 = trends1['rounds']
    rounds2 = trends2['rounds']
    
    # 找到共同的轮次范围
    min_round = max(min(rounds1), min(rounds2))
    max_round = min(max(rounds1), max(rounds2))
    common_rounds = list(range(min_round, max_round + 1))
    
    # 提取对应轮次的数据
    def extract_common_data(trends, common_rounds):
        """提取共同轮次的数据"""
        original_rounds = trends['rounds']
        common_data = {
            'rounds': common_rounds,
            'total': {'susceptible': [], 'infected': [], 'recovered': []},
            'intellectual': {'susceptible': [], 'infected': [], 'recovered': []},
            'regular': {'susceptible': [], 'infected': [], 'recovered': []}
        }
        
        for round_num in common_rounds:
            if round_num in original_rounds:
                idx = original_rounds.index(round_num)
                for group in ['total', 'intellectual', 'regular']:
                    for state in ['susceptible', 'infected', 'recovered']:
                        common_data[group][state].append(trends[group][state][idx])
            else:
                # 如果某轮次数据缺失，填充0
                for group in ['total', 'intellectual', 'regular']:
                    for state in ['susceptible', 'infected', 'recovered']:
                        common_data[group][state].append(0)
        
        return common_data
    
    trends1_common = extract_common_data(trends1, common_rounds)
    trends2_common = extract_common_data(trends2, common_rounds)
    
    logger.info(f"使用共同轮次范围: {min_round}-{max_round}, 共{len(common_rounds)}轮")
    
    # 1. 绘制总群体SIR对比曲线
    plt.figure(figsize=(6, 3))

    # 创建进度标签（使用百分数格式）
    total_rounds = len(common_rounds)
    period_labels = [f"{(i+1)/total_rounds*100:.0f}%" for i in range(total_rounds)]

    # 第一个目录（深色）
    plt.plot(common_rounds, trends1_common['total']['susceptible'], 'o-', color=colors_dark['susceptible'],
             linewidth=2, markersize=1.5, label=f'{label1} - Susceptible')
    plt.plot(common_rounds, trends1_common['total']['infected'], 's-', color=colors_dark['infected'],
             linewidth=2, markersize=1.5, label=f'{label1} - Infected')
    plt.plot(common_rounds, trends1_common['total']['recovered'], '^-', color=colors_dark['recovered'],
             linewidth=2, markersize=1.5, label=f'{label1} - Recovered')

    # 第二个目录（浅色）
    plt.plot(common_rounds, trends2_common['total']['susceptible'], 'o--', color=colors_light['susceptible'],
             linewidth=2, markersize=1.5, label=f'{label2} - Susceptible')
    plt.plot(common_rounds, trends2_common['total']['infected'], 's--', color=colors_light['infected'],
             linewidth=2, markersize=1.5, label=f'{label2} - Infected')
    plt.plot(common_rounds, trends2_common['total']['recovered'], '^--', color=colors_light['recovered'],
             linewidth=2, markersize=1.5, label=f'{label2} - Recovered')

    plt.xlabel('Period', fontsize=10)
    plt.ylabel('Proportion', fontsize=10)

    # 设置x轴刻度和标签 - 保留5-8个刻度
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [common_rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    plt.xticks(selected_rounds, selected_labels, fontsize=10, rotation=45)
    plt.yticks(fontsize=10)

    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)

    # 图例 - 放在外面，无框
    plt.legend(fontsize=8, bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
    plt.tight_layout()

    # 保存PNG格式
    total_output_path_png = os.path.join(comparison_dir, 'total_sir_comparison.png')
    plt.savefig(total_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    total_output_path_pdf = os.path.join(comparison_dir, 'total_sir_comparison.pdf')
    plt.savefig(total_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"总群体SIR对比曲线已保存到: {total_output_path_png}")
    logger.info(f"总群体SIR对比曲线PDF已保存到: {total_output_path_pdf}")

    # 2. 绘制知识分子群体SIR对比曲线
    plt.figure(figsize=(6, 3))

    # 第一个目录（深色）
    plt.plot(common_rounds, trends1_common['intellectual']['susceptible'], 'o-', color=colors_dark['susceptible'],
             linewidth=2, markersize=1.5, label=f'{label1} - Susceptible')
    plt.plot(common_rounds, trends1_common['intellectual']['infected'], 's-', color=colors_dark['infected'],
             linewidth=2, markersize=1.5, label=f'{label1} - Infected')
    plt.plot(common_rounds, trends1_common['intellectual']['recovered'], '^-', color=colors_dark['recovered'],
             linewidth=2, markersize=1.5, label=f'{label1} - Recovered')

    # 第二个目录（浅色）
    plt.plot(common_rounds, trends2_common['intellectual']['susceptible'], 'o--', color=colors_light['susceptible'],
             linewidth=2, markersize=1.5, label=f'{label2} - Susceptible')
    plt.plot(common_rounds, trends2_common['intellectual']['infected'], 's--', color=colors_light['infected'],
             linewidth=2, markersize=1.5, label=f'{label2} - Infected')
    plt.plot(common_rounds, trends2_common['intellectual']['recovered'], '^--', color=colors_light['recovered'],
             linewidth=2, markersize=1.5, label=f'{label2} - Recovered')

    plt.xlabel('Period', fontsize=10)
    plt.ylabel('Proportion', fontsize=10)

    plt.xticks(selected_rounds, selected_labels, fontsize=10, rotation=45)
    plt.yticks(fontsize=10)

    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)

    # 图例 - 放在外面，无框
    plt.legend(fontsize=8, bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
    plt.tight_layout()

    # 保存PNG格式
    intellectual_output_path_png = os.path.join(comparison_dir, 'intellectual_sir_comparison.png')
    plt.savefig(intellectual_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    intellectual_output_path_pdf = os.path.join(comparison_dir, 'intellectual_sir_comparison.pdf')
    plt.savefig(intellectual_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"知识分子群体SIR对比曲线已保存到: {intellectual_output_path_png}")
    logger.info(f"知识分子群体SIR对比曲线PDF已保存到: {intellectual_output_path_pdf}")

    # 3. 绘制普通群众群体SIR对比曲线
    plt.figure(figsize=(6, 3))

    # 第一个目录（深色）
    plt.plot(common_rounds, trends1_common['regular']['susceptible'], 'o-', color=colors_dark['susceptible'],
             linewidth=2, markersize=1.5, label=f'{label1} - Susceptible')
    plt.plot(common_rounds, trends1_common['regular']['infected'], 's-', color=colors_dark['infected'],
             linewidth=2, markersize=1.5, label=f'{label1} - Infected')
    plt.plot(common_rounds, trends1_common['regular']['recovered'], '^-', color=colors_dark['recovered'],
             linewidth=2, markersize=1.5, label=f'{label1} - Recovered')

    # 第二个目录（浅色）
    plt.plot(common_rounds, trends2_common['regular']['susceptible'], 'o--', color=colors_light['susceptible'],
             linewidth=2, markersize=1.5, label=f'{label2} - Susceptible')
    plt.plot(common_rounds, trends2_common['regular']['infected'], 's--', color=colors_light['infected'],
             linewidth=2, markersize=1.5, label=f'{label2} - Infected')
    plt.plot(common_rounds, trends2_common['regular']['recovered'], '^--', color=colors_light['recovered'],
             linewidth=2, markersize=1.5, label=f'{label2} - Recovered')

    plt.xlabel('Period', fontsize=10)
    plt.ylabel('Proportion', fontsize=10)

    plt.xticks(selected_rounds, selected_labels, fontsize=10, rotation=45)
    plt.yticks(fontsize=10)

    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)

    # 图例 - 放在外面，无框
    plt.legend(fontsize=8, bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
    plt.tight_layout()

    # 保存PNG格式
    regular_output_path_png = os.path.join(comparison_dir, 'regular_sir_comparison.png')
    plt.savefig(regular_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    regular_output_path_pdf = os.path.join(comparison_dir, 'regular_sir_comparison.pdf')
    plt.savefig(regular_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"普通群众群体SIR对比曲线已保存到: {regular_output_path_png}")
    logger.info(f"普通群众群体SIR对比曲线PDF已保存到: {regular_output_path_pdf}")

    # 4. 保存对比数据JSON
    save_comparison_data_json(trends1_common, trends2_common, label1, label2, comparison_dir)

    # 5. 生成对比分析报告
    generate_comparison_report(trends1_common, trends2_common, label1, label2, comparison_dir)

def save_comparison_data_json(trends1: dict, trends2: dict, label1: str, label2: str, output_dir: str):
    """
    保存对比数据为JSON文件

    Args:
        trends1: 第一个目录的趋势数据
        trends2: 第二个目录的趋势数据
        label1: 第一个目录的标签
        label2: 第二个目录的标签
        output_dir: 输出目录
    """
    # 构建输出数据
    output_data = {
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "comparison_type": "SIR curves comparison",
            "dataset1": label1,
            "dataset2": label2,
            "total_rounds": len(trends1['rounds']),
            "rounds_range": f"{min(trends1['rounds'])}-{max(trends1['rounds'])}",
            "description": "SIR model comparison data for virtual community experiment"
        },
        "rounds": trends1['rounds'],
        "dataset1": {
            "label": label1,
            "total_population": {
                "susceptible": trends1['total']['susceptible'],
                "infected": trends1['total']['infected'],
                "recovered": trends1['total']['recovered']
            },
            "intellectual_group": {
                "susceptible": trends1['intellectual']['susceptible'],
                "infected": trends1['intellectual']['infected'],
                "recovered": trends1['intellectual']['recovered']
            },
            "regular_group": {
                "susceptible": trends1['regular']['susceptible'],
                "infected": trends1['regular']['infected'],
                "recovered": trends1['regular']['recovered']
            }
        },
        "dataset2": {
            "label": label2,
            "total_population": {
                "susceptible": trends2['total']['susceptible'],
                "infected": trends2['total']['infected'],
                "recovered": trends2['total']['recovered']
            },
            "intellectual_group": {
                "susceptible": trends2['intellectual']['susceptible'],
                "infected": trends2['intellectual']['infected'],
                "recovered": trends2['intellectual']['recovered']
            },
            "regular_group": {
                "susceptible": trends2['regular']['susceptible'],
                "infected": trends2['regular']['infected'],
                "recovered": trends2['regular']['recovered']
            }
        }
    }

    # 保存JSON文件
    json_output_path = os.path.join(output_dir, 'sir_comparison_data.json')
    with open(json_output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    logger.info(f"SIR对比数据已保存到: {json_output_path}")

def generate_comparison_report(trends1: dict, trends2: dict, label1: str, label2: str, output_dir: str):
    """
    生成对比分析报告

    Args:
        trends1: 第一个目录的趋势数据
        trends2: 第二个目录的趋势数据
        label1: 第一个目录的标签
        label2: 第二个目录的标签
        output_dir: 输出目录
    """
    rounds = trends1['rounds']
    total_rounds = len(rounds)

    # 计算关键指标
    def calculate_metrics(data):
        """计算各种指标"""
        susceptible = data['susceptible']
        infected = data['infected']
        recovered = data['recovered']

        # 初始和最终状态
        initial_s, initial_i, initial_r = susceptible[0], infected[0], recovered[0]
        final_s, final_i, final_r = susceptible[-1], infected[-1], recovered[-1]

        # 感染峰值
        max_infected = max(infected)
        max_infected_round = infected.index(max_infected) + 1

        # 恢复率变化
        recovery_change = final_r - initial_r

        # 易感人群减少率
        susceptible_reduction = initial_s - final_s

        return {
            'initial': {'s': initial_s, 'i': initial_i, 'r': initial_r},
            'final': {'s': final_s, 'i': final_i, 'r': final_r},
            'max_infected': max_infected,
            'max_infected_round': max_infected_round,
            'recovery_change': recovery_change,
            'susceptible_reduction': susceptible_reduction
        }

    # 计算两个数据集的指标
    metrics1 = {
        'total': calculate_metrics(trends1['total']),
        'intellectual': calculate_metrics(trends1['intellectual']),
        'regular': calculate_metrics(trends1['regular'])
    }

    metrics2 = {
        'total': calculate_metrics(trends2['total']),
        'intellectual': calculate_metrics(trends2['intellectual']),
        'regular': calculate_metrics(trends2['regular'])
    }

    # 生成报告内容
    report_content = f"""虚拟社区SIR模型对比分析报告

生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
对比数据集：{label1} vs {label2}
分析轮次：第{rounds[0]}轮至第{rounds[-1]}轮，共{total_rounds}轮

==================================================
一、总体传播情况对比
==================================================

1. 感染峰值对比：
   - {label1}：{metrics1['total']['max_infected']:.1%}（第{metrics1['total']['max_infected_round']}轮）
   - {label2}：{metrics2['total']['max_infected']:.1%}（第{metrics2['total']['max_infected_round']}轮）
   - 差异：{abs(metrics1['total']['max_infected'] - metrics2['total']['max_infected']):.1%}

2. 最终恢复率对比：
   - {label1}：{metrics1['total']['final']['r']:.1%}
   - {label2}：{metrics2['total']['final']['r']:.1%}
   - 差异：{abs(metrics1['total']['final']['r'] - metrics2['total']['final']['r']):.1%}

3. 易感人群减少对比：
   - {label1}：{metrics1['total']['susceptible_reduction']:.1%}
   - {label2}：{metrics2['total']['susceptible_reduction']:.1%}
   - 差异：{abs(metrics1['total']['susceptible_reduction'] - metrics2['total']['susceptible_reduction']):.1%}

==================================================
二、知识分子群体对比
==================================================

1. 感染峰值对比：
   - {label1}：{metrics1['intellectual']['max_infected']:.1%}（第{metrics1['intellectual']['max_infected_round']}轮）
   - {label2}：{metrics2['intellectual']['max_infected']:.1%}（第{metrics2['intellectual']['max_infected_round']}轮）
   - 差异：{abs(metrics1['intellectual']['max_infected'] - metrics2['intellectual']['max_infected']):.1%}

2. 最终恢复率对比：
   - {label1}：{metrics1['intellectual']['final']['r']:.1%}
   - {label2}：{metrics2['intellectual']['final']['r']:.1%}
   - 差异：{abs(metrics1['intellectual']['final']['r'] - metrics2['intellectual']['final']['r']):.1%}

==================================================
三、普通群众群体对比
==================================================

1. 感染峰值对比：
   - {label1}：{metrics1['regular']['max_infected']:.1%}（第{metrics1['regular']['max_infected_round']}轮）
   - {label2}：{metrics2['regular']['max_infected']:.1%}（第{metrics2['regular']['max_infected_round']}轮）
   - 差异：{abs(metrics1['regular']['max_infected'] - metrics2['regular']['max_infected']):.1%}

2. 最终恢复率对比：
   - {label1}：{metrics1['regular']['final']['r']:.1%}
   - {label2}：{metrics2['regular']['final']['r']:.1%}
   - 差异：{abs(metrics1['regular']['final']['r'] - metrics2['regular']['final']['r']):.1%}

==================================================
四、对比总结
==================================================

基于{total_rounds}轮的对比分析，两个数据集在SIR传播模式上的主要差异：

1. 传播强度：{'数据集1传播更强' if metrics1['total']['max_infected'] > metrics2['total']['max_infected'] else '数据集2传播更强' if metrics2['total']['max_infected'] > metrics1['total']['max_infected'] else '传播强度相近'}

2. 恢复能力：{'数据集1恢复能力更强' if metrics1['total']['final']['r'] > metrics2['total']['final']['r'] else '数据集2恢复能力更强' if metrics2['total']['final']['r'] > metrics1['total']['final']['r'] else '恢复能力相近'}

3. 群体差异：知识分子群体和普通群众群体在两个数据集中表现出{'显著' if max(abs(metrics1['intellectual']['max_infected'] - metrics1['regular']['max_infected']), abs(metrics2['intellectual']['max_infected'] - metrics2['regular']['max_infected'])) > 0.1 else '轻微'}的差异模式

==================================================
报告结束
==================================================
"""

    # 保存报告
    report_path = os.path.join(output_dir, 'SIR对比分析报告.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)

    logger.info(f"对比分析报告已保存到: {report_path}")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='比较两个目录的SIR曲线图')
    parser.add_argument('dir1', help='第一个目录路径')
    parser.add_argument('dir2', help='第二个目录路径')
    parser.add_argument('--label1', default='Dataset1', help='第一个数据集的标签（默认：Dataset1）')
    parser.add_argument('--label2', default='Dataset2', help='第二个数据集的标签（默认：Dataset2）')

    args = parser.parse_args()

    logger.info("开始SIR曲线对比分析...")

    # 1. 加载两个目录的SIR数据
    sir_data1 = load_sir_data(args.dir1)
    if not sir_data1:
        logger.error(f"未找到第一个目录的SIR数据: {args.dir1}")
        return 1

    sir_data2 = load_sir_data(args.dir2)
    if not sir_data2:
        logger.error(f"未找到第二个目录的SIR数据: {args.dir2}")
        return 1

    # 2. 提取趋势数据
    trends1 = extract_sir_trends(sir_data1)
    if not trends1:
        logger.error("提取第一个目录的趋势数据失败")
        return 1

    trends2 = extract_sir_trends(sir_data2)
    if not trends2:
        logger.error("提取第二个目录的趋势数据失败")
        return 1

    # 3. 绘制对比曲线图
    plot_comparison_sir_curves(trends1, trends2, args.label1, args.label2)

    logger.info("SIR曲线对比分析完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
