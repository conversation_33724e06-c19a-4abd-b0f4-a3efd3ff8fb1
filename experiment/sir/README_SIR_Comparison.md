# SIR曲线绘制和对比工具使用说明

## 概述

本目录包含两个主要的SIR（Susceptible-Infected-Recovered）模型分析工具：

1. `plot_sir_curves.py` - 单个目录的SIR曲线绘制工具
2. `compare_sir_curves.py` - 两个目录的SIR曲线对比工具

## 功能特点

### 字体设置
- 所有图表均使用 **Times New Roman** 字体
- 确保学术论文的专业性和一致性

### 颜色方案
- 使用莫兰迪色系，柔和美观
- 对比工具中使用深色和浅色版本进行区分

### 输出格式
- 同时生成PNG和PDF格式的图表
- 包含详细的中文分析报告
- 保存JSON格式的原始数据

## 使用方法

### 1. 单个目录SIR曲线绘制

```bash
# 使用最新的日志目录（自动查找）
python experiment/sir/plot_sir_curves.py

# 指定特定的日志目录
python experiment/sir/plot_sir_curves.py experiment/analysis_results/run_20250823_212449
```

**输出文件：**
- `total_sir_curves.png/pdf` - 总群体SIR曲线
- `intellectual_sir_curves.png/pdf` - 知识分子群体SIR曲线
- `regular_sir_curves.png/pdf` - 普通群众群体SIR曲线
- `sir_curves_data.json` - 原始数据
- `SIR传播分析报告.txt` - 中文分析报告

### 2. 两个目录SIR曲线对比

```bash
# 基本用法
python experiment/sir/compare_sir_curves.py dir1 dir2

# 带自定义标签
python experiment/sir/compare_sir_curves.py \
    experiment/analysis_results/run_20250820_192149 \
    experiment/analysis_results/run_20250823_212449 \
    --label1 "实验组A" --label2 "实验组B"
```

**输出文件：**
- `total_sir_comparison.png/pdf` - 总群体对比曲线
- `intellectual_sir_comparison.png/pdf` - 知识分子群体对比曲线
- `regular_sir_comparison.png/pdf` - 普通群众群体对比曲线
- `sir_comparison_data.json` - 对比数据
- `SIR对比分析报告.txt` - 中文对比分析报告

## 图表特点

### 曲线样式
- **Susceptible（易感）**: 圆形标记 (o)，莫兰迪绿色
- **Infected（感染）**: 方形标记 (s)，莫兰迪红色
- **Recovered（恢复）**: 三角形标记 (^)，莫兰迪蓝色

### 对比图表
- **第一个数据集**: 实线，深色莫兰迪色系
- **第二个数据集**: 虚线，浅色莫兰迪色系

### 坐标轴
- X轴：Period（时期），显示为百分比进度
- Y轴：Proportion（比例），范围0-1
- 网格：半透明，便于读取数值

## 数据要求

程序会自动查找目录中的 `*sir_metrics.json` 文件，这些文件应包含：
- `round`: 轮次信息
- `stage`: 阶段信息（before/after）
- `susceptible`, `infected`, `recovered`: 各状态的比例数据
- `group_analysis`: 群体分析数据（intellectual/regular）

## 输出目录结构

```
experiment/sir/
├── run_YYYYMMDD_HHMMSS/          # 单个分析结果
│   ├── total_sir_curves.png
│   ├── total_sir_curves.pdf
│   ├── intellectual_sir_curves.png
│   ├── intellectual_sir_curves.pdf
│   ├── regular_sir_curves.png
│   ├── regular_sir_curves.pdf
│   ├── sir_curves_data.json
│   └── SIR传播分析报告.txt
└── comparison_Label1_vs_Label2/   # 对比分析结果
    ├── total_sir_comparison.png
    ├── total_sir_comparison.pdf
    ├── intellectual_sir_comparison.png
    ├── intellectual_sir_comparison.pdf
    ├── regular_sir_comparison.png
    ├── regular_sir_comparison.pdf
    ├── sir_comparison_data.json
    └── SIR对比分析报告.txt
```

## 注意事项

1. 确保输入目录包含有效的SIR数据文件
2. 对比分析会自动找到两个数据集的共同轮次范围
3. 所有图表都使用Times New Roman字体，适合学术发表
4. 生成的PDF文件适合高质量打印和论文插图使用

## 示例

查看生成的示例文件：
- `experiment/sir/run_20250823_212449/` - 单个分析示例
- `experiment/sir/comparison_Run_20250820_vs_Run_20250823/` - 对比分析示例
